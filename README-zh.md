# ToolHub - 专业在线工具聚合平台

<div align="center">

![ToolHub Logo](public/logo.jpg)

**为开发者、系统管理员和技术专业人员打造的综合性网络工具聚合平台**

[![Next.js](https://img.shields.io/badge/Next.js-15.2.3-black?style=flat-square&logo=next.js)](https://nextjs.org/)
[![React](https://img.shields.io/badge/React-18.2.0-blue?style=flat-square&logo=react)](https://reactjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.x-blue?style=flat-square&logo=typescript)](https://www.typescriptlang.org/)
[![Prisma](https://img.shields.io/badge/Prisma-6.5.0-2D3748?style=flat-square&logo=prisma)](https://www.prisma.io/)
[![License](https://img.shields.io/badge/License-MIT-green?style=flat-square)](LICENSE)

[🌐 在线演示](https://toolbox-web.vercel.app) | [📖 文档](docs/) | [🐛 报告问题](https://github.com/wenhaofree/toolbox-web/issues) | [✨ 功能请求](https://github.com/wenhaofree/toolbox-web/issues)

</div>

## 🚀 关于 ToolHub

ToolHub 是一个现代化的综合性在线工具聚合平台，在统一界面中集成了 **35+ 专业工具**，涵盖多个类别。基于 Next.js 15、React 18 和 TypeScript 构建，为开发者、系统管理员和技术专业人员提供强大、高效的日常工作流程解决方案。

### 🎯 为什么选择 ToolHub？

- **🔧 35+ 专业工具**：全面的工具集合，涵盖域名/IP 工具、开发工具、安全工具、图像处理等多个领域
- **🌍 完整国际化**：使用 next-intl 实现的完整多语言支持，包含中文和英文语言环境
- **🎨 现代化 UI/UX**：基于 Radix UI 组件和 Tailwind CSS 构建的简洁、响应式设计
- **⚡ 高性能**：采用 Next.js 15 App Router 和 Turbopack 的服务端渲染，实现闪电般的开发速度
- **🔐 安全认证**：通过 NextAuth.js 集成 Google 和 GitHub 的 OAuth 认证
- **💳 高级功能**：集成 Stripe 支付系统，支持高级功能和付费工具
- **📱 移动优先设计**：完全响应式界面，在所有设备和屏幕尺寸上都能完美运行
- **🛡️ 隐私保护**：敏感操作采用客户端处理，确保您的数据不会离开浏览器

## 🛠️ 工具分类与功能

### 🌐 域名与 IP 工具
- **Whois 查询**：查询全面的域名注册信息，包括所有者、注册商和到期日期
- **IP 地理定位**：通过 IP 地址定位详细的地理数据，包括国家、城市、坐标和 ISP 信息
- **DNS 查询**：全面的 DNS 记录分析，支持 A、AAAA、MX、TXT、NS 等记录类型
- **域名信息**：完整的域名分析，包括服务器详情、HTTP 头信息和 SSL 证书信息

### 💻 开发工具
- **JSON 格式化器**：格式化、验证和美化 JSON 数据，支持语法高亮和错误检测
- **代码压缩器**：压缩 JavaScript、CSS 和 HTML 代码以减少文件大小
- **正则表达式测试器**：实时匹配和解释功能的正则表达式测试和调试
- **Base64 编解码器**：支持文本和文件输入的 Base64 字符串编码和解码
- **JWT 解码器**：解码和分析 JSON Web Token，支持头部、载荷和签名验证
- **文本对比**：文件对比和差异高亮显示，支持并排或统一视图

### 🔒 安全与加密工具
- **SHA 计算器**：生成 SHA-1、SHA-256、SHA-512 等安全哈希算法
- **MD5 哈希**：生成 MD5 哈希值，支持碰撞检测功能
- **AES 加密**：高级 AES 加密/解密，支持多种密钥长度（128/192/256位）
- **DES 加密**：DES 加密/解密，兼容传统系统
- **随机生成器**：生成安全的随机密码、数字和字符串
- **随机 IP 生成器**：为测试和开发创建随机 IP 地址

### 🖼️ 图像处理
- **图像压缩**：在保持质量的同时减少图像文件大小，支持自定义压缩设置
- **图像调整大小**：调整图像到指定尺寸，支持保持宽高比
- **图像水印**：添加文本或图像水印，支持自定义位置和透明度
- **颜色提取器**：从上传的图像中提取主要颜色调色板

### 📊 数据转换与处理
- **JSON 转 YAML**：在 JSON 和 YAML 格式之间转换，支持正确格式化
- **YAML 转 JSON**：将 YAML 数据转换为 JSON 格式，支持验证
- **CSV 转 JSON**：CSV 数据与 JSON 格式之间的相互转换
- **YAML 格式化器**：格式化和验证 YAML 文件，支持语法高亮
- **颜色转换器**：在不同颜色格式（HEX、RGB、HSL、HSV）之间转换

### 📝 文本处理工具
- **字数统计**：统计单词、字符、段落和阅读时间
- **大小写转换**：在大写、小写、标题格式和驼峰命名之间转换文本
- **URL 编解码**：支持各种字符集的 URL 编码和解码
- **二维码生成器**：创建可自定义大小、颜色和错误纠正的二维码

## ✨ 技术特性

### 🏗️ 核心架构
- **Next.js 15** 配合 App Router 和 React 18，实现现代化 Web 开发
- **TypeScript 5.x** 提供类型安全开发和更好的代码质量
- **Turbopack** 支持闪电般快速的开发构建
- **服务端渲染 (SSR)** 优化性能和 SEO

### 🎨 用户界面与体验
- **Radix UI 组件** 提供可访问的、无样式的 UI 原语
- **Tailwind CSS 3.4** 实用优先的样式和响应式设计
- **Framer Motion** 流畅的动画和过渡效果
- **Lucide React** 一致、美观的图标
- **深色/浅色主题** 支持 next-themes

### 🌐 国际化与本地化
- **next-intl 3.26.3** 全面的国际化支持
- **中文 (zh)** 和 **英文 (en)** 语言支持
- **基于语言环境的路由** 自动语言检测
- **RTL 支持** 为未来扩展做好准备

### 🔐 认证与安全
- **NextAuth.js 4.24.11** 安全认证
- **OAuth 提供商**：Google 和 GitHub 集成
- **会话管理** 安全的 Cookie 处理
- **CSRF 保护** 和安全头

### 💳 支付与订阅
- **Stripe 17.5.0** 集成支付处理
- **订阅管理** 循环计费
- **Webhook 处理** 实时支付更新
- **订单跟踪** 和支付历史

### 📊 数据库与数据管理
- **Prisma ORM 6.5.0** 类型安全的数据库操作
- **PostgreSQL** 生产环境支持
- **数据库迁移** 和架构管理
- **连接池** 优化性能

### 🛠️ 开发工具与实用程序
- **ESLint** 代码质量和一致性
- **PostCSS** 高级 CSS 处理
- **Date-fns 4.1.0** 日期操作和格式化
- **UUID 生成** 唯一标识符
- **Sonner** 优雅的提示通知

## 📋 环境要求

- **Node.js** 18.17 或更高版本
- **pnpm** 8.0 或更高版本（推荐）或 npm/yarn
- **PostgreSQL** 12.0 或更高版本（生产环境）
- **Git** 版本控制

## 🚀 快速开始

### 1. 克隆仓库

```bash
git clone https://github.com/wenhaofree/toolbox-web.git
cd toolbox-web
```

### 2. 安装依赖

```bash
# 使用 pnpm（推荐）
pnpm install

# 或使用 npm
npm install

# 或使用 yarn
yarn install
```

### 3. 环境配置

在根目录创建 `.env.local` 文件：

```bash
cp .env.example .env.local
```

配置以下环境变量：

| 变量 | 描述 | 示例 |
|------|------|------|
| `DATABASE_URL` | PostgreSQL 数据库连接 URL | `postgresql://user:password@localhost:5432/toolhub` |
| `NEXTAUTH_SECRET` | NextAuth.js 密钥 | `your-super-secret-key-here` |
| `NEXTAUTH_URL` | 应用程序基础 URL | `http://localhost:3000` |
| `AUTH_GOOGLE_ID` | Google OAuth 客户端 ID | `your-google-oauth-id` |
| `AUTH_GOOGLE_SECRET` | Google OAuth 客户端密钥 | `your-google-oauth-secret` |
| `AUTH_GITHUB_ID` | GitHub OAuth 应用 ID | `your-github-oauth-id` |
| `AUTH_GITHUB_SECRET` | GitHub OAuth 应用密钥 | `your-github-oauth-secret` |
| `STRIPE_PUBLIC_KEY` | Stripe 可发布密钥 | `pk_test_...` |
| `STRIPE_PRIVATE_KEY` | Stripe 私密密钥 | `sk_test_...` |
| `STRIPE_WEBHOOK_SECRET` | Stripe Webhook 端点密钥 | `whsec_...` |

### 4. 数据库设置

```bash
# 生成 Prisma 客户端
pnpm db:generate

# 推送数据库架构
pnpm db:push

# （可选）打开 Prisma Studio 查看/编辑数据
pnpm db:studio
```

### 5. 启动开发服务器

```bash
pnpm dev
```

在浏览器中打开 [http://localhost:3000](http://localhost:3000) 查看应用程序。

## 📜 可用脚本

```bash
# 开发
pnpm dev          # 使用 Turbopack 启动开发服务器
pnpm build        # 构建生产版本
pnpm start        # 启动生产服务器
pnpm lint         # 运行 ESLint 代码分析

# 数据库操作
pnpm db:generate  # 生成 Prisma 客户端
pnpm db:push      # 推送架构更改到数据库
pnpm db:pull      # 从数据库拉取架构
pnpm db:studio    # 打开 Prisma Studio（数据库 GUI）
pnpm db:sync      # 同步数据库架构（pull + push + generate）
```

## 🚀 部署

### 部署到 Vercel（推荐）

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https%3A%2F%2Fgithub.com%2Fwenhaofree%2Ftoolbox-web&env=DATABASE_URL,NEXTAUTH_SECRET,NEXTAUTH_URL,AUTH_GOOGLE_ID,AUTH_GOOGLE_SECRET,AUTH_GITHUB_ID,AUTH_GITHUB_SECRET,STRIPE_PUBLIC_KEY,STRIPE_PRIVATE_KEY,STRIPE_WEBHOOK_SECRET&project-name=toolhub&repository-name=toolhub)

1. **Fork 此仓库** 到您的 GitHub 账户
2. **在 [Vercel](https://vercel.com) 创建新项目**
3. **导入您 fork 的仓库**
4. **配置环境变量**（参见环境配置部分）
5. **部署** - Vercel 将自动构建和部署您的应用程序

### 手动部署步骤

1. **准备数据库**：设置 PostgreSQL 数据库（推荐：[Supabase](https://supabase.com)、[PlanetScale](https://planetscale.com) 或 [Neon](https://neon.tech)）
2. **在部署平台配置环境变量**
3. **构建应用程序**：`pnpm build`
4. **启动生产服务器**：`pnpm start`

### 其他部署平台

- **Netlify**：使用 Next.js 构建插件
- **Railway**：连接您的 GitHub 仓库
- **DigitalOcean App Platform**：直接从 GitHub 部署
- **AWS Amplify**：为 Next.js 配置构建设置

## 📁 项目结构

```
toolbox-web/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── [locale]/          # 国际化路由
│   │   │   ├── tools/         # 工具页面
│   │   │   └── layout.tsx     # 语言环境布局
│   │   ├── api/               # API 路由
│   │   ├── auth/              # 认证页面
│   │   └── globals.css        # 全局样式
│   ├── components/            # React 组件
│   │   ├── ui/                # 可重用 UI 组件
│   │   ├── Header.tsx         # 网站头部
│   │   └── Footer.tsx         # 网站底部
│   ├── lib/                   # 实用程序库
│   │   ├── tools/             # 工具相关实用程序
│   │   └── prisma.ts          # Prisma 客户端
│   ├── data/                  # 静态数据和配置
│   ├── hooks/                 # 自定义 React Hooks
│   ├── i18n/                  # 国际化配置
│   ├── types/                 # TypeScript 类型定义
│   ├── auth.config.ts         # NextAuth 配置
│   └── middleware.ts          # Next.js 中间件
├── prisma/
│   └── schema.prisma          # 数据库架构
├── public/                    # 静态资源
├── messages/                  # 国际化消息文件
│   ├── en.json               # 英文翻译
│   └── zh.json               # 中文翻译
├── components.json            # shadcn/ui 配置
├── next.config.mjs           # Next.js 配置
├── tailwind.config.ts        # Tailwind CSS 配置
└── package.json              # 依赖和脚本
```

## 🤝 贡献

我们欢迎社区的贡献！以下是您可以帮助的方式：

### 开始贡献

1. **Fork 仓库** 在 GitHub 上
2. **本地克隆您的 fork**：
   ```bash
   git clone https://github.com/your-username/toolbox-web.git
   cd toolbox-web
   ```
3. **创建功能分支**：
   ```bash
   git checkout -b feature/amazing-new-tool
   ```
4. **进行更改** 并彻底测试
5. **提交更改**：
   ```bash
   git commit -m "为开发者添加令人惊叹的新工具"
   ```
6. **推送到您的 fork**：
   ```bash
   git push origin feature/amazing-new-tool
   ```
7. **在 GitHub 上创建 Pull Request**

### 贡献指南

- **代码质量**：遵循现有代码风格并使用 TypeScript
- **测试**：确保您的更改不会破坏现有功能
- **文档**：为新功能更新文档
- **国际化**：为英文和中文添加翻译
- **可访问性**：确保新组件具有可访问性

### 添加新工具

1. 在 `src/app/[locale]/tools/your-tool-name/` 中创建新页面
2. 将工具元数据添加到 `src/lib/tools/data.ts`
3. 将翻译添加到 `messages/en.json` 和 `messages/zh.json`
4. 更新 `src/lib/tools/utils.ts` 中的工具列表
5. 彻底测试工具

## 📄 许可证

本项目采用 MIT 许可证 - 详情请参见 [LICENSE](LICENSE) 文件。

## 📞 联系与支持

- **作者**：[WenHaoFree](https://github.com/wenhaofree)
- **邮箱**：[<EMAIL>](mailto:<EMAIL>)
- **GitHub**：[https://github.com/wenhaofree](https://github.com/wenhaofree)
- **项目仓库**：[https://github.com/wenhaofree/toolbox-web](https://github.com/wenhaofree/toolbox-web)
- **问题反馈**：[报告错误或请求功能](https://github.com/wenhaofree/toolbox-web/issues)

## 🙏 致谢

- [Next.js](https://nextjs.org/) 提供出色的 React 框架
- [Radix UI](https://www.radix-ui.com/) 提供可访问的 UI 组件
- [Tailwind CSS](https://tailwindcss.com/) 提供实用优先的样式
- [Prisma](https://www.prisma.io/) 提供类型安全的数据库访问
- [NextAuth.js](https://next-auth.js.org/) 提供认证功能
- [Vercel](https://vercel.com/) 提供托管和部署服务

---

<div align="center">

**⭐ 如果您觉得这个项目有帮助，请给它一个星标！**

[🌐 在线演示](https://toolbox-web.vercel.app) • [📖 文档](docs/) • [🐛 问题](https://github.com/wenhaofree/toolbox-web/issues) • [💬 讨论](https://github.com/wenhaofree/toolbox-web/discussions)

</div>
