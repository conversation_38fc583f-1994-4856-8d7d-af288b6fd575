# ToolHub - Professional Online Tool Aggregation Platform

<div align="center">

![ToolHub Logo](public/logo.jpg)

**A comprehensive web-based tool aggregation platform for developers, system administrators, and technical professionals**

[![Next.js](https://img.shields.io/badge/Next.js-15.2.3-black?style=flat-square&logo=next.js)](https://nextjs.org/)
[![React](https://img.shields.io/badge/React-18.2.0-blue?style=flat-square&logo=react)](https://reactjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.x-blue?style=flat-square&logo=typescript)](https://www.typescriptlang.org/)
[![Prisma](https://img.shields.io/badge/Prisma-6.5.0-2D3748?style=flat-square&logo=prisma)](https://www.prisma.io/)
[![License](https://img.shields.io/badge/License-MIT-green?style=flat-square)](LICENSE)

[🌐 Live Demo](https://toolbox-web.vercel.app) | [📖 Documentation](docs/) | [🐛 Report Bug](https://github.com/wenhaofree/toolbox-web/issues) | [✨ Request Feature](https://github.com/wenhaofree/toolbox-web/issues)

</div>

## 🚀 About ToolHub

ToolHub is a modern, comprehensive online tool aggregation platform that brings together **35+ professional tools** across multiple categories in one unified interface. Built with Next.js 15, React 18, and TypeScript, it provides developers, system administrators, and technical professionals with a powerful, efficient solution for their daily workflow needs.

### 🎯 Why Choose ToolHub?

- **🔧 35+ Professional Tools**: Comprehensive collection spanning domain/IP utilities, development tools, security utilities, image processing, and more
- **🌍 Full Internationalization**: Complete multilingual support with Chinese and English locales using next-intl
- **🎨 Modern UI/UX**: Clean, responsive design built with Radix UI components and Tailwind CSS
- **⚡ High Performance**: Server-side rendering with Next.js 15 App Router and Turbopack for lightning-fast development
- **🔐 Secure Authentication**: OAuth integration with Google and GitHub via NextAuth.js
- **💳 Premium Features**: Stripe payment integration for advanced functionalities and premium tools
- **📱 Mobile-First Design**: Fully responsive interface that works seamlessly across all devices and screen sizes
- **🛡️ Privacy-Focused**: Client-side processing for sensitive operations ensures your data never leaves your browser

## 🛠️ Tool Categories & Features

### 🌐 Domain & IP Tools
- **Whois Lookup**: Query comprehensive domain registration information including owner, registrar, and expiration dates
- **IP Geolocation**: Locate IP addresses with detailed geographic data including country, city, coordinates, and ISP information
- **DNS Lookup**: Comprehensive DNS record analysis supporting A, AAAA, MX, TXT, NS, and other record types
- **Domain Information**: Complete domain analysis including server details, HTTP headers, and SSL certificate information

### 💻 Development Tools
- **JSON Formatter**: Format, validate, and beautify JSON data with syntax highlighting and error detection
- **Code Compressor**: Minify JavaScript, CSS, and HTML code to reduce file sizes
- **Regex Tester**: Test and debug regular expressions with real-time matching and explanation
- **Base64 Encoder/Decoder**: Encode and decode Base64 strings with support for text and file inputs
- **JWT Decoder**: Decode and analyze JSON Web Tokens with header, payload, and signature verification
- **Text Diff**: Compare text files and highlight differences with side-by-side or unified view

### 🔒 Security & Encryption Tools
- **SHA Calculator**: Generate SHA-1, SHA-256, SHA-512, and other secure hash algorithms
- **MD5 Hash**: Generate MD5 hashes with collision detection capabilities
- **AES Encryption**: Advanced AES encryption/decryption with multiple key sizes (128/192/256-bit)
- **DES Encryption**: DES encryption/decryption for legacy system compatibility
- **Random Generator**: Generate secure random passwords, numbers, and strings
- **Random IP Generator**: Create random IP addresses for testing and development

### 🖼️ Image Processing
- **Image Compression**: Reduce image file sizes while maintaining quality with customizable compression settings
- **Image Resize**: Resize images to specific dimensions with aspect ratio preservation
- **Image Watermark**: Add text or image watermarks with customizable positioning and opacity
- **Color Extractor**: Extract dominant color palettes from uploaded images

### 📊 Data Conversion & Processing
- **JSON to YAML**: Convert between JSON and YAML formats with proper formatting
- **YAML to JSON**: Transform YAML data to JSON with validation
- **CSV to JSON**: Convert CSV data to JSON format and vice versa
- **YAML Formatter**: Format and validate YAML files with syntax highlighting
- **Color Converter**: Convert between different color formats (HEX, RGB, HSL, HSV)

### � Text Processing Tools
- **Word Counter**: Count words, characters, paragraphs, and reading time
- **Case Converter**: Convert text between uppercase, lowercase, title case, and camelCase
- **URL Encoder/Decoder**: Encode and decode URLs with support for various character sets
- **QR Code Generator**: Create customizable QR codes with adjustable size, colors, and error correction

## ✨ Technical Features

### 🏗️ Core Architecture
- **Next.js 15** with App Router and React 18 for modern web development
- **TypeScript 5.x** for type-safe development and better code quality
- **Turbopack** support for lightning-fast development builds
- **Server-Side Rendering (SSR)** for optimal performance and SEO

### 🎨 User Interface & Experience
- **Radix UI Components** for accessible, unstyled UI primitives
- **Tailwind CSS 3.4** for utility-first styling and responsive design
- **Framer Motion** for smooth animations and transitions
- **Lucide React** for consistent, beautiful icons
- **Dark/Light Theme** support with next-themes

### 🌐 Internationalization & Localization
- **next-intl 3.26.3** for comprehensive i18n support
- **Chinese (zh)** and **English (en)** language support
- **Locale-based routing** with automatic language detection
- **RTL support** ready for future expansion

### 🔐 Authentication & Security
- **NextAuth.js 4.24.11** for secure authentication
- **OAuth providers**: Google and GitHub integration
- **Session management** with secure cookie handling
- **CSRF protection** and secure headers

### � Payment & Subscription
- **Stripe 17.5.0** integration for payment processing
- **Subscription management** with recurring billing
- **Webhook handling** for real-time payment updates
- **Order tracking** and payment history

### 📊 Database & Data Management
- **Prisma ORM 6.5.0** for type-safe database operations
- **PostgreSQL** support for production environments
- **Database migrations** and schema management
- **Connection pooling** for optimal performance

### 🛠️ Development Tools & Utilities
- **ESLint** for code quality and consistency
- **PostCSS** for advanced CSS processing
- **Date-fns 4.1.0** for date manipulation and formatting
- **UUID generation** for unique identifiers
- **Sonner** for elegant toast notifications

## 📋 Prerequisites

- **Node.js** 18.17 or higher
- **pnpm** 8.0 or higher (recommended) or npm/yarn
- **PostgreSQL** 12.0 or higher (for production)
- **Git** for version control

## 🚀 Quick Start

### 1. Clone the Repository

```bash
git clone https://github.com/wenhaofree/toolbox-web.git
cd toolbox-web
```

### 2. Install Dependencies

```bash
# Using pnpm (recommended)
pnpm install

# Or using npm
npm install

# Or using yarn
yarn install
```

### 3. Environment Configuration

Create a `.env.local` file in the root directory:

```bash
cp .env.example .env.local
```

Configure the following environment variables:

| Variable | Description | Example |
|----------|-------------|---------|
| `DATABASE_URL` | PostgreSQL database connection URL | `postgresql://user:password@localhost:5432/toolhub` |
| `NEXTAUTH_SECRET` | NextAuth.js secret key | `your-super-secret-key-here` |
| `NEXTAUTH_URL` | Application base URL | `http://localhost:3000` |
| `AUTH_GOOGLE_ID` | Google OAuth Client ID | `your-google-oauth-id` |
| `AUTH_GOOGLE_SECRET` | Google OAuth Client Secret | `your-google-oauth-secret` |
| `AUTH_GITHUB_ID` | GitHub OAuth App ID | `your-github-oauth-id` |
| `AUTH_GITHUB_SECRET` | GitHub OAuth App Secret | `your-github-oauth-secret` |
| `STRIPE_PUBLIC_KEY` | Stripe publishable key | `pk_test_...` |
| `STRIPE_PRIVATE_KEY` | Stripe secret key | `sk_test_...` |
| `STRIPE_WEBHOOK_SECRET` | Stripe webhook endpoint secret | `whsec_...` |

### 4. Database Setup

```bash
# Generate Prisma client
pnpm db:generate

# Push database schema
pnpm db:push

# (Optional) Open Prisma Studio to view/edit data
pnpm db:studio
```

### 5. Start Development Server

```bash
pnpm dev
```

Open [http://localhost:3000](http://localhost:3000) in your browser to see the application.

## 📜 Available Scripts

```bash
# Development
pnpm dev          # Start development server with Turbopack
pnpm build        # Build for production
pnpm start        # Start production server
pnpm lint         # Run ESLint code analysis

# Database Operations
pnpm db:generate  # Generate Prisma client
pnpm db:push      # Push schema changes to database
pnpm db:pull      # Pull schema from database
pnpm db:studio    # Open Prisma Studio (database GUI)
pnpm db:sync      # Sync database schema (pull + push + generate)
```

## 🚀 Deployment

### Deploy to Vercel (Recommended)

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https%3A%2F%2Fgithub.com%2Fwenhaofree%2Ftoolbox-web&env=DATABASE_URL,NEXTAUTH_SECRET,NEXTAUTH_URL,AUTH_GOOGLE_ID,AUTH_GOOGLE_SECRET,AUTH_GITHUB_ID,AUTH_GITHUB_SECRET,STRIPE_PUBLIC_KEY,STRIPE_PRIVATE_KEY,STRIPE_WEBHOOK_SECRET&project-name=toolhub&repository-name=toolhub)

1. **Fork this repository** to your GitHub account
2. **Create a new project** on [Vercel](https://vercel.com)
3. **Import your forked repository**
4. **Configure environment variables** (see Environment Configuration section)
5. **Deploy** - Vercel will automatically build and deploy your application

### Manual Deployment Steps

1. **Prepare your database**: Set up a PostgreSQL database (recommended: [Supabase](https://supabase.com), [PlanetScale](https://planetscale.com), or [Neon](https://neon.tech))
2. **Configure environment variables** in your deployment platform
3. **Build the application**: `pnpm build`
4. **Start the production server**: `pnpm start`

### Other Deployment Platforms

- **Netlify**: Use the Next.js build plugin
- **Railway**: Connect your GitHub repository
- **DigitalOcean App Platform**: Deploy directly from GitHub
- **AWS Amplify**: Configure build settings for Next.js

## 📁 Project Structure

```
toolbox-web/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── [locale]/          # Internationalized routes
│   │   │   ├── tools/         # Tool pages
│   │   │   └── layout.tsx     # Locale layout
│   │   ├── api/               # API routes
│   │   ├── auth/              # Authentication pages
│   │   └── globals.css        # Global styles
│   ├── components/            # React components
│   │   ├── ui/                # Reusable UI components
│   │   ├── Header.tsx         # Site header
│   │   └── Footer.tsx         # Site footer
│   ├── lib/                   # Utility libraries
│   │   ├── tools/             # Tool-related utilities
│   │   └── prisma.ts          # Prisma client
│   ├── data/                  # Static data and configurations
│   ├── hooks/                 # Custom React hooks
│   ├── i18n/                  # Internationalization config
│   ├── types/                 # TypeScript type definitions
│   ├── auth.config.ts         # NextAuth configuration
│   └── middleware.ts          # Next.js middleware
├── prisma/
│   └── schema.prisma          # Database schema
├── public/                    # Static assets
├── messages/                  # i18n message files
│   ├── en.json               # English translations
│   └── zh.json               # Chinese translations
├── components.json            # shadcn/ui configuration
├── next.config.mjs           # Next.js configuration
├── tailwind.config.ts        # Tailwind CSS configuration
└── package.json              # Dependencies and scripts
```

## 🤝 Contributing

We welcome contributions from the community! Here's how you can help:

### Getting Started

1. **Fork the repository** on GitHub
2. **Clone your fork** locally:
   ```bash
   git clone https://github.com/your-username/toolbox-web.git
   cd toolbox-web
   ```
3. **Create a feature branch**:
   ```bash
   git checkout -b feature/amazing-new-tool
   ```
4. **Make your changes** and test thoroughly
5. **Commit your changes**:
   ```bash
   git commit -m "Add amazing new tool for developers"
   ```
6. **Push to your fork**:
   ```bash
   git push origin feature/amazing-new-tool
   ```
7. **Create a Pull Request** on GitHub

### Contribution Guidelines

- **Code Quality**: Follow the existing code style and use TypeScript
- **Testing**: Ensure your changes don't break existing functionality
- **Documentation**: Update documentation for new features
- **Internationalization**: Add translations for both English and Chinese
- **Accessibility**: Ensure new components are accessible

### Adding New Tools

1. Create a new page in `src/app/[locale]/tools/your-tool-name/`
2. Add tool metadata to `src/lib/tools/data.ts`
3. Add translations to `messages/en.json` and `messages/zh.json`
4. Update the tool list in `src/lib/tools/utils.ts`
5. Test the tool thoroughly

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 📞 Contact & Support

- **Author**: [WenHaoFree](https://github.com/wenhaofree)
- **Email**: [<EMAIL>](mailto:<EMAIL>)
- **GitHub**: [https://github.com/wenhaofree](https://github.com/wenhaofree)
- **Project Repository**: [https://github.com/wenhaofree/toolbox-web](https://github.com/wenhaofree/toolbox-web)
- **Issues**: [Report bugs or request features](https://github.com/wenhaofree/toolbox-web/issues)

## 🙏 Acknowledgments

- [Next.js](https://nextjs.org/) for the amazing React framework
- [Radix UI](https://www.radix-ui.com/) for accessible UI components
- [Tailwind CSS](https://tailwindcss.com/) for utility-first styling
- [Prisma](https://www.prisma.io/) for type-safe database access
- [NextAuth.js](https://next-auth.js.org/) for authentication
- [Vercel](https://vercel.com/) for hosting and deployment

---

<div align="center">

**⭐ Star this repository if you find it helpful!**

[🌐 Live Demo](https://toolbox-web.vercel.app) • [📖 Documentation](docs/) • [🐛 Issues](https://github.com/wenhaofree/toolbox-web/issues) • [💬 Discussions](https://github.com/wenhaofree/toolbox-web/discussions)

</div>
